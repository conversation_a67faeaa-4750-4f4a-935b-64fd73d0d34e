<?xml version="1.0" encoding="utf-8"?>

<!-- web.config 변환 사용에 대한 자세한 내용은 http://go.microsoft.com/fwlink/?LinkId=125889를 참조하세요. -->

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <!--
    아래 예에서, "Match" 로케이터가 "MyDB" 값을 가진 "name" 특성을 
 찾은 경우에만 "SetAttributes" 변환에서 "ReleaseSQLServer"를 
 사용하도록 "connectionString"의 값을 변경합니다.
    
    <connectionStrings>
      <add name="MyDB" 
        connectionString="Data Source=ReleaseSQLServer;Initial Catalog=MyReleaseDB;Integrated Security=True" 
        xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </connectionStrings>
  -->
  <system.web>
    <compilation xdt:Transform="RemoveAttributes(debug)" />
    <!--
      
      아래 예에서 "Replace" 변환은 web.config 파일의 
      <customErrors> 섹션 전체를 바꿉니다.
      <system.web> 노드 아래에는 customErrors 섹션이 하나만 있기 때문에 
      "xdt:Locator" 특성을 사용할 필요가 없습니다.
      
      <customErrors defaultRedirect="GenericError.htm"
        mode="RemoteOnly" xdt:Transform="Replace">
        <error statusCode="500" redirect="InternalError.htm"/>
      </customErrors>
    >
    -->
  </system.web>
</configuration>